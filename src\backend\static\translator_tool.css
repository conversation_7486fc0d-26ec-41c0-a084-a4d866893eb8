/* Translation Tool Styles */

/* ===================== ANIMATIONS ===================== */
@keyframes fadeInUp {
  0% {
    opacity: 0;
    transform: translateY(40px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeIn {
  0% {
    opacity: 0;
  }
  100% {
    opacity: 1;
  }
}

@keyframes popIn {
  0% {
    opacity: 0;
    transform: scale(0.85);
  }
  80% {
    opacity: 1;
    transform: scale(1.05);
  }
  100% {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes shimmer {
  0% {
    background-position: -400px 0;
  }
  100% {
    background-position: 400px 0;
  }
}

/* ===================== ANIMATION CLASSES ===================== */
.animate-fadeInUp {
  animation: fadeInUp 0.7s cubic-bezier(0.4,0,0.2,1) both;
}

.animate-fadeIn {
  animation: fadeIn 0.6s ease both;
}

.animate-popIn {
  animation: popIn 0.5s cubic-bezier(0.4,0,0.2,1) both;
}

.animate-shimmer {
  background: linear-gradient(90deg, #f8f9fa 25%, #e9ecef 50%, #f8f9fa 75%);
  background-size: 400px 100%;
  animation: shimmer 1.2s linear infinite;
}

/* ===================== APPLY ANIMATIONS TO ELEMENTS ===================== */

/* ===================== ANIMATION USAGE ===================== */
/* Main container: fade in from below for page entrance */
.translation-container {
    animation: fadeInUp 0.7s cubic-bezier(0.4,0,0.2,1) both;
}

/* Card: pop in for focus */
.translation-card {
    animation: popIn 0.5s cubic-bezier(0.4,0,0.2,1) both;
}

/* Card header: fade in for subtlety */
.card-header-custom {
    animation: fadeIn 0.8s 0.1s cubic-bezier(0.4,0,0.2,1) both;
}

/* Upload area: fade in from below, slightly delayed */
.upload-area {
    animation: fadeInUp 0.8s 0.15s cubic-bezier(0.4,0,0.2,1) both;
}

/* File info and loading spinner: fade in for smoothness */
.file-info {
    animation: fadeIn 0.7s 0.2s cubic-bezier(0.4,0,0.2,1) both;
}
.loading-spinner {
    animation: fadeIn 0.7s 0.2s cubic-bezier(0.4,0,0.2,1) both;
}

/* Language grid, excel options, progress: fade in from below */
.language-grid, .excel-options, .progress-container {
    animation: fadeInUp 0.7s 0.2s cubic-bezier(0.4,0,0.2,1) both;
}

/* Buttons: pop in for interactivity */
.download-link-custom, .translate-link-custom, .remove-file-btn, #toggleAllColumns, .select-all-btn {
    animation: popIn 0.5s cubic-bezier(0.4,0,0.2,1) both;
}

/* Progress bar: smooth width and color transitions */
.progress-bar-custom {
    transition: width 0.8s cubic-bezier(0.4,0,0.2,1), background 0.4s;
}

/* Spinner: spin and fade in */
.loading-spinner .spinner {
    animation: spin 1s linear infinite, fadeIn 0.5s both;
}

/* Shimmer effect for loading states */
.loading-spinner.animate-shimmer {
    animation: shimmer 1.2s linear infinite, fadeIn 0.5s both;
}

/* Select2 dropdown: fade in for multi-select */
.select2-container--default .select2-selection--multiple {
    animation: fadeIn 0.5s cubic-bezier(0.4,0,0.2,1) both;
}

/* Excel options header: fade in for section header */
.excel-options-header {
    animation: fadeIn 0.6s cubic-bezier(0.4,0,0.2,1) both;
}

/* Base font family for all elements */
body, html, .translation-container, .card, .form-control, .btn {
    font-family: "Segoe UI", -apple-system, BlinkMacSystemFont, "Roboto", "Oxygen", "Ubuntu", "Cantarell", "Fira Sans", "Droid Sans", "Helvetica Neue", sans-serif;
}

:root{
    /* New color palette from design specs */
    --corporate-primary: #1E1F41;
    --corporate-secondary: #7B869C;
    --corporate-light: #DFE7EA;
    --golden-primary: #7D653F;
    --golden-secondary: #9E8664;
    --golden-light: #BFAF8F;
    --warm-grey-primary: #85827A;
    --warm-grey-secondary: #BFBAB0;
    --warm-grey-light: #DFD5D2;
    --deep-teal-primary: #24639F;
    --deep-teal-secondary: #526D70;
    --deep-teal-light: #BCD4D2;
    --sustainability-primary: #6E826F;
    --sustainability-secondary: #A8B580;
    --sustainability-light: #C4D69A;
}

/* Translation Tool Styles */
.translation-container {
    background: transparent;
    min-height: calc(100vh - 140px);
    padding: 2rem 0;
}

.translation-card {
    background: white;
    border-radius: 16px;
    box-shadow: 0 8px 32px rgba(30, 31, 65, 0.1);
    border: 1px solid rgba(30, 31, 65, 0.08);
    overflow: hidden;
}

.card-header-custom {
    background: linear-gradient(0deg, var(--corporate-primary) 65%, var(--corporate-secondary) 100%);
    color: white;
    font-weight: 700;
    padding: 2rem;
    border: none;
    position: relative;
    overflow: hidden;
}

.header-icon-topright {
    position: absolute;
    top: 1.5rem;
    right: 2rem;
    font-size: 5rem;
    color: #fff;
    background: rgba(30,31,65,0.12);
    border-radius: 12px;
    padding: 0.5rem 0.7rem;
    box-shadow: 0 2px 8px rgba(30,31,65,0.08);
    z-index: 2;
    display: flex;
    align-items: center;
    justify-content: center;
}

@media (max-width: 768px) {
    .header-icon-topright {
        top: 1rem;
        right: 1rem;
        font-size: 5rem;
        padding: 0.35rem 0.5rem;
    }
}

.upload-area {
    border: 2px dashed var(--corporate-secondary);
    border-radius: 12px;
    padding: 3rem 2rem;
    text-align: center;
    background: linear-gradient(135deg, rgba(36, 99, 159, 0.05) 0%, rgba(60, 120, 180, 0.05) 100%);
    transition: all 0.3s ease;
    cursor: pointer;
}

.upload-area:hover {
    border-color: var(--deep-teal-primary);
    background: linear-gradient(135deg, rgba(36, 99, 159, 0.1) 0%, rgba(60, 120, 180, 0.05) 100%);
    transform: translateY(-2px);
}

.upload-area.dragover {
    border-color: var(--sustainability-primary);
    background: linear-gradient(135deg, rgba(110, 130, 111, 0.1) 0%, rgba(60, 120, 180, 0.05) 100%);
}

.upload-icon {
    font-size: 3rem;
    color: var(--deep-teal-primary);
    margin-bottom: 1rem;
}

/* Match .file-info style to .loading-spinner for consistency */
.file-info {
    background-color: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    padding: 1rem;
    margin-top: 1rem;
}

.language-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1.5rem;
    margin: 1.5rem 0;
}


.excel-options {
    background: linear-gradient(135deg, rgba(85, 117, 172, 0.05) 0%, rgba(191, 175, 143, 0.05) 100%);
    border: 1px solid var(--corporate-secondary);
    border-radius: 12px;
    padding: 1.5rem;
    margin-top: 1.5rem;
}

.form-check-label {
    white-space: normal !important;
    word-break: break-word;
    overflow-wrap: anywhere;
    font-size: 0.8rem;
}

.column-checkbox {
    margin: 0.5rem 0;
}

.column-checkbox input[type="checkbox"] {
    margin-right: 0.5rem;
    transform: scale(1.2);
}


.progress-container {
    margin-top: 2rem;
    padding: 1.5rem;
    background: linear-gradient(135deg, var(--corporate-light) 0%, var(--corporate-secondary) 100%);
    border-radius: 12px;
    border: 1px solid var(--corporate-secondary);
}

.progress-custom {
    height: 8px;
    border-radius: 4px;
    background-color: var(--warm-grey-light);
}

.progress-bar-custom {
    background: linear-gradient(90deg, var(--corporate-primary) 0%, var(--corporate-secondary) 100%);
    border-radius: 4px;
    transition: width 0.3s ease;
}

.status-text {
    color: var(--corporate-primary);
    font-weight: 500;
    margin-top: 0.5rem;
}

/* Version display styles */
#runningEnvDisplay {
    font-size: 0.75rem;
    font-weight: bold;
    letter-spacing: 0.5px;
}

#runningEnvDisplay.bg-secondary {
    background-color: var(--epr-spartan-blue) !important;
}

#versionDisplay {
    font-size: 0.85rem;
    color: rgba(255, 255, 255, 0.8) !important;
    transition: color 0.2s ease;
}

#versionDisplay:hover {
    color: white !important;
    text-decoration: underline !important;
}

/* Environment-specific colors */
#runningEnvDisplay[data-env="LOCAL"] {
    background-color: var(--epr-green) !important;
}

#runningEnvDisplay[data-env="DEVEL"] {
    background-color: var(--epr-gold) !important;
    color: var(--epr-blue) !important;
}

#runningEnvDisplay[data-env="STAGING"] {
    background-color: var(--epr-danger) !important;
}

#runningEnvDisplay[data-env=""] {
    background-color: var(--epr-blue) !important;
}

/* Translator-specific styles */
.translation-container #runningEnvDisplay {
    position: fixed;
    top: 10px;
    right: 10px;
    z-index: 1050;
}

@media (max-width: 768px) {
    .language-grid {
        grid-template-columns: 1fr;
        gap: 1rem;
    }

    .translation-container {
        padding: 1rem 0;
    }

    .card-header-custom {
        padding: 1.5rem;
    }

    .upload-area {
        padding: 2rem 1rem;
    }
}

/* Toggle All Columns Button Styles */
#toggleAllColumns {
    transition: all 0.3s ease;
    border: 2px solid var(--warm-grey-secondary, #BFBAB0);
    color: black;
    background-color:  whitesmoke;
    border-radius: 8px;
    font-weight: 500;
    padding: 8px 16px;
    font-size: 0.9rem;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    min-width: 130px;
}

#toggleAllColumns:hover {
    background-color: var(--corporate-light);
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.15);
}

#toggleAllColumns:active {
    transform: translateY(0px);
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

#toggleAllColumns i {
    transition: transform 0.2s ease;
    font-size: 1rem;
}

#toggleAllColumns:hover i {
    transform: scale(1.1);
}

/* Excel Options Header */
.excel-options-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
}

.excel-options-header h5 {
    margin: 0;
    flex-grow: 1;
}

/* Professional Progress Bar Styles */
.progress-custom {
    height: 16px;
    border-radius: 12px;
    background: linear-gradient(135deg, var(--corporate-light) 0%, var(--corporate-secondary) 100%);
    border: none;
    box-shadow: 
        inset 0 2px 4px rgba(0, 0, 0, 0.1),
        0 1px 2px rgba(0, 0, 0, 0.05);
    overflow: hidden;
    position: relative;
}

.progress-custom::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(90deg, 
        rgba(255, 255, 255, 0.1) 0%, 
        rgba(255, 255, 255, 0.3) 50%, 
        rgba(255, 255, 255, 0.1) 100%);
    pointer-events: none;
}

.progress-bar-custom {
    background: linear-gradient(135deg, 
        var(--corporate-primary, #1E1F41) 0%,
        #4a5568 25%,
        #2d3748 50%,
        var(--corporate-primary, #1E1F41) 100%);
    border-radius: 10px;
    transition: all 0.8s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    overflow: hidden;
    box-shadow: 
        0 2px 8px rgba(30, 31, 65, 0.3),
        inset 0 1px 2px rgba(255, 255, 255, 0.2);
}

.progress-bar-custom::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 50%;
    background: linear-gradient(180deg, 
        rgba(255, 255, 255, 0.25) 0%, 
        rgba(255, 255, 255, 0.05) 100%);
    border-radius: 10px 10px 0 0;
}

.status-text {
    font-size: 0.95rem;
    color: #4a5568;
    margin-top: 0.75rem;
    font-weight: 600;
    text-align: left;
    letter-spacing: 0.025em;
}

/* Progress container enhancements */
.progress-container {
    background: #ffffff;
    border: 1px solid #e2e8f0;
    border-radius: 12px;
    padding: 1.5rem;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
    margin-top: 1.5rem;
}

.progress-container .d-flex {
    margin-bottom: 0.75rem;
}

.progress-container .fw-semibold {
    font-size: 1rem;
    color: var(--corporate-primary, #1E1F41);
    font-weight: 600;
}

#progressPercent {
    font-size: 1rem;
    font-weight: 700;
    color: var(--corporate-primary, #1E1F41);
    background: linear-gradient(135deg, #f7fafc 0%, #edf2f7 100%);
    padding: 0.25rem 0.75rem;
    border-radius: 6px;
    border: 1px solid #e2e8f0;
    min-width: 60px;
    text-align: center;
}

/* Loading Spinner Styles */
.loading-spinner {
    display: none;
    text-align: center;
    padding: 20px;
    background-color: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    margin-top: 15px;
}

.spinner {
    width: 30px;
    height: 30px;
    border: 3px solid #e9ecef;
    border-top: 3px solid var(--corporate-primary, #1E1F41);
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto 10px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.loading-text {
    color: #6c757d;
    font-size: 0.9rem;
    font-weight: 500;
}

/* Download Link Custom Styles */
.download-link-custom {
    background: var(--corporate-primary, #1E1F41);
    color: #fff;
    border: none;
    border-radius: 8px;
    font-size: 1.15rem;
    font-weight: 600;
    padding: 0.85rem 2.5rem;
    box-shadow: 0 2px 8px rgba(30, 31, 65, 0.10);
    transition: background 0.2s, transform 0.15s, box-shadow 0.15s;
    display: inline-flex;
    align-items: center;
    gap: 0.75rem;
    margin: 0 auto;
    letter-spacing: 0.01em;
}

.download-link-custom:hover, .download-link-custom:focus {
    background: #23244a;
    color: #fff;
    transform: translateY(-2px) scale(1.03);
    box-shadow: 0 4px 16px rgba(30, 31, 65, 0.16);
    text-decoration: none;
}

.download-link-custom i {
    font-size: 1.3rem;
}

.download-link-wrapper {
    width: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    margin-top: 2rem;
}

/* Start Translation Button Custom Style (Deep Teal) */
.translate-link-custom {
    background: var(--deep-teal-primary);
    color: #fff;
    border: none;
    border-radius: 8px;
    font-size: 1.15rem;
    font-weight: 600;
    padding: 0.85rem 2.5rem;
    box-shadow: 0 2px 8px rgba(0, 123, 138, 0.10);
    transition: background 0.2s, transform 0.15s, box-shadow 0.15s;
    display: inline-flex;
    align-items: center;
    gap: 0.75rem;
    margin: 0 auto;
    letter-spacing: 0.01em;
    opacity: 1;
    cursor: pointer;
}

.translate-link-custom:hover, .translate-link-custom:focus {
    background: var(--deep-teal-primary);
    color: #fff;
    transform: translateY(-2px) scale(1.03);
    box-shadow: 0 4px 16px rgba(0, 123, 138, 0.16);
    text-decoration: none;
}

.translate-link-custom:disabled,
.translate-link-custom[disabled] {
    background: var(--warm-grey-primary);
    color: #e0e0e0;
    opacity: 0.7;
    cursor: not-allowed;
    box-shadow: none;
    transform: none;
}

.translate-link-custom i {
    font-size: 1.3rem;
}


/* Custom Select All Button Styles */
.select-all-btn {
    border-radius: 1.5rem;
    font-size: 1rem;
    transition: background 0.2s, box-shadow 0.2s;
    box-shadow: 0 2px 8px rgba(0,0,0,0.07);
    padding-left: 1.5rem;
    padding-right: 1.5rem;
    padding-top: 0.5rem;
    padding-bottom: 0.5rem;
    background: var(--warm-grey-secondary);
    border: none;
}
.select-all-btn:hover, .select-all-btn:focus {
    background: var(--warm-grey-primary);
    box-shadow: 0 4px 16px rgba(37,99,235,0.15);
}


/* Select2 Multiple Languages Custom Styles */
.select2-container--default .select2-selection--multiple {
    min-height: 38px;
    border: 1px solid #ced4da;
    border-radius: 0.375rem;
    padding: 0.25rem 0.5rem;
    font-size: 0.85rem;
  }

  /* Remove File Button Warm Gray Style */
.remove-file-btn {
    background: var(--corporate-light);
    color: var(--corporate-primary);
    border: 1px solid var(--warm-grey-primary);
    border-radius: 6px;
    font-size: 0.95rem;
    font-weight: 500;
    padding: 0.4rem 0.8rem;
    margin-left: 1rem;
    transition: background 0.2s, color 0.2s, box-shadow 0.2s;
    box-shadow: 0 2px 6px rgba(133, 130, 122, 0.08);
    cursor: pointer;
}

.remove-file-btn:hover, .remove-file-btn:focus {
    background: var(--warm-grey-primary);
    color: #fff;
    box-shadow: 0 4px 12px rgba(133, 130, 122, 0.15);
    text-decoration: none;
}