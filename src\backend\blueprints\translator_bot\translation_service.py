import json
import pandas as pd
from openai import AzureOpenAI
from openpyxl import load_workbook
import time
import os
import tempfile
from typing import List, Dict, <PERSON><PERSON>
from flask import current_app
from config.config import TranslatorBotConfig


class DirectTranslationService:
    """
    Handles Excel file translation using direct API calls to Azure OpenAI.
    """
    def translate_multiple_languages(self, column_names: List[str], target_languages: List[str], max_rows: int = 200, file_context: str = None, translation_id: str = None, session_obj=None) -> Dict:
        """
        Translate the specified columns into multiple target languages, saving a new file for each language.

        Args:
            column_names (List[str]): List of column names or letters to translate
            target_languages (List[str]): List of target languages
            max_rows (int): Maximum rows per batch for API calls
            file_context (str, optional): Additional context for translation
            translation_id (str, optional): Unique ID for tracking translation progress
            session_obj (optional): Session object for storing progress information

        Returns:
            Dict: Summary with per-language results and file paths, and a zip file path if multiple languages
        """
        import shutil
        import zipfile
        results = []
        excel_files = []
        original_excel_path = self.excel_path
        original_filename = os.path.basename(original_excel_path)
        original_base, original_ext = os.path.splitext(original_filename)

        # Calculate total_batches as the sum of all batches for all columns and languages
        total_batches = 0
        batches_per_col_lang = {}
        for lang in target_languages:
            for col in column_names:
                try:
                    json_arrays, _ = self.read_excel_column_to_json(col, max_rows)
                    batches_per_col_lang[(lang, col)] = len(json_arrays)
                    total_batches += len(json_arrays)
                except Exception as e:
                    current_app.logger.warning(f"Could not calculate batches for column {col} in language {lang}: {e}")
                    batches_per_col_lang[(lang, col)] = 0

        # Setup progress tracking ONCE if session_obj and translation_id are provided
        current_batch = 0
        if session_obj is not None and translation_id is not None:
            try:
                if 'translation_progress' not in session_obj:
                    session_obj['translation_progress'] = {}
                session_obj['translation_progress'][translation_id] = {
                    'current_batch': 0,
                    'total_batches': total_batches,
                    'status': 'processing',
                    'download_url': ''
                }
                session_obj.modified = True
                current_app.logger.info(f"Initialized progress tracking for {translation_id}: 0/{total_batches} batches")
            except Exception as e:
                current_app.logger.error(f"Failed to initialize progress tracking: {e}")
                # Continue without progress tracking

        # Helper function to safely update progress
        def update_progress():
            if session_obj is not None and translation_id is not None:
                try:
                    nonlocal current_batch
                    current_batch += 1
                    prog = session_obj['translation_progress'][translation_id]
                    prog['current_batch'] = current_batch
                    session_obj.modified = True
                    current_app.logger.debug(f"Progress updated: {current_batch}/{total_batches} batches")
                except Exception as e:
                    current_app.logger.error(f"Failed to update progress: {e}")

        for lang in target_languages:
            # Prepare a new file for this language, using original filename + language suffix
            lang_file_name = f"{original_base}_{lang}{original_ext}"
            lang_file = os.path.join(self.upload_dir, lang_file_name)
            shutil.copy2(original_excel_path, lang_file)

            # Temporarily point self.excel_path to the new file
            prev_excel_path = self.excel_path
            self.excel_path = lang_file
            try:
                for col in column_names:
                    # For each column, translate in batches and update progress for each batch
                    try:
                        json_arrays, _ = self.read_excel_column_to_json(col, max_rows)
                    except Exception as e:
                        current_app.logger.error(f"Error reading column {col}: {e}")
                        lang_result = {
                            'success': False,
                            'error': f'Error reading column {col}: {str(e)}',
                            'output_file': lang_file
                        }
                        results.append({
                            'language': lang,
                            'column': col,
                            'result': lang_result
                        })
                        continue

                    batch_results = []
                    for json_obj in json_arrays:
                        # Update progress for each batch
                        update_progress()

                        # Use the same prompt as in translate_column
                        prompt = self.get_default_prompt(lang)
                        try:
                            response = self.submit_to_gpt(json_obj, prompt, file_context=file_context)
                            batch_results.append(response)
                        except Exception as e:
                            current_app.logger.error(f"Error in GPT translation: {e}")
                            batch_results.append(json.dumps({}))

                    # Write results to Excel for this column
                    try:
                        self.write_results_to_excel(batch_results, None, col)
                        lang_result = {
                            'success': True,
                            'batches_processed': len(batch_results),
                            'output_file': lang_file,
                            'original_column': col,
                            'target_language': lang
                        }
                    except Exception as e:
                        current_app.logger.error(f"Error writing results for column {col}: {e}")
                        lang_result = {
                            'success': False,
                            'error': f'Error writing results for column {col}: {str(e)}',
                            'output_file': lang_file
                        }
                    results.append({
                        'language': lang,
                        'column': col,
                        'result': lang_result
                    })
                excel_files.append(lang_file)
            except Exception as e:
                current_app.logger.error(f"Error processing language {lang}: {e}")
                lang_result = {
                    'success': False,
                    'error': str(e),
                    'output_file': lang_file
                }
                results.append({
                    'language': lang,
                    'result': lang_result
                })
            # Restore original excel_path
            self.excel_path = prev_excel_path
        # Mark as completed
        if session_obj is not None and translation_id is not None:
            try:
                prog = session_obj['translation_progress'][translation_id]
                prog['status'] = 'completed'
                prog['current_batch'] = total_batches  # Ensure progress shows 100%
                prog['download_url'] = f'/translator/api/download/{translation_id}'
                session_obj.modified = True
                current_app.logger.info(f"Translation {translation_id} marked as completed")
            except Exception as e:
                current_app.logger.error(f"Failed to mark translation as completed: {e}")

        summary = {
            'success': any(r['result']['success'] for r in results),
            'languages': target_languages,
            'results': results,
        }
        return summary

    
    def __init__(self, user_id: str, original_filename: str = None):
        self.user_id = user_id
        self.config = TranslatorBotConfig()

        # Create independent upload directory for translator bot
        self.temp_base_dir = os.path.join(tempfile.gettempdir(), 'translator-bot-uploads')
        os.makedirs(self.temp_base_dir, exist_ok=True)
        self.upload_dir = os.path.join(self.temp_base_dir, f'user_{user_id}')
        os.makedirs(self.upload_dir, exist_ok=True)

        # Always set self.original_filename
        if original_filename is not None:
            self.original_filename = original_filename
        else:
            self.original_filename = f"data_{user_id}.xlsx"

        self.excel_path = os.path.join(self.upload_dir, self.original_filename)

        # Initialize Azure OpenAI client using TranslatorBotConfig
        self.client = AzureOpenAI(
            api_key=self.config.openai_llm_key,
            api_version=self.config.api_version,
            azure_endpoint=self.config.api_llm_endpoint,
        )

        # Store deployment name for later use
        self.deployment_name = self.config.llm_deployed

    def cleanup(self):
        """
        Remove the user's temporary upload directory and all its contents.
        """
        try:
            import shutil
            if os.path.exists(self.upload_dir):
                shutil.rmtree(self.upload_dir)
                current_app.logger.info(f"Cleaned up temp directory for user {self.user_id}")
        except Exception as e:
            current_app.logger.error(f"Failed to clean up temp directory for user {self.user_id}: {e}")
        
    def read_excel_column_to_json(self, column_name: str, max_rows: int = 200) -> Tuple[List[Dict], str]:
        """
        Read a column from an Excel file and transform it into JSON arrays with pagination.
        
        Args:
            column_name (str): Column letter or name to read
            max_rows (int): Maximum number of rows per JSON array (pagination)
            
        Returns:
            Tuple[List[Dict], str]: List of JSON objects and header value
        """
        # Load the Excel file
        df = pd.read_excel(self.excel_path)
        
        # Convert column name to proper format if it's a letter
        if len(column_name) == 1 and column_name.isalpha():
            try:
                column_name = df.columns[ord(column_name.upper()) - ord('A')]
            except IndexError:
                raise ValueError(f"Column {column_name} not found in the Excel file")
        
        # Check if the column exists
        if column_name not in df.columns:
            raise ValueError(f"Column {column_name} not found in the Excel file")
        
        # Get the header
        header = column_name
        
        # Get the column data (excluding header)
        column_data = df[column_name].fillna("").astype(str)
        
        # Create JSON arrays with pagination
        json_arrays = []
        for i in range(0, len(column_data), max_rows):
            chunk = column_data.iloc[i:i+max_rows]
            json_obj = {
                "column": column_name,
                "content": {str(j+1+i): value for j, value in enumerate(chunk)}
            }
            json_arrays.append(json_obj)
        
        return json_arrays, header
    
    def submit_to_gpt(self, json_obj: Dict, prompt: str, file_context: str = None) -> str:
        """
        Submit a JSON object to Azure GPT with a translation prompt.
        
        Args:
            json_obj (Dict): JSON object to submit
            prompt (str): Translation prompt to use
            file_context (str, optional): Additional file context to include in the user message
        Returns:
            str: Response from GPT
        """
        start_call = time.time()
        current_app.logger.info(f"Sending request to OpenAI with payload of {len(json.dumps(json_obj))} characters...")
        try:
            # Prepare the messages
            messages = [
                {"role": "system", "content": prompt},
            ]
            if file_context:
                user_content = f"Additional context for the translation model:\n{file_context}\n\n{json.dumps(json_obj)}"
            else:
                user_content = json.dumps(json_obj)
            messages.append({"role": "user", "content": user_content})

            current_app.logger.info(f"OpenAI messages: {messages}")

            
            # Make the API request using Azure OpenAI client
            response = self.client.chat.completions.create(
                model=self.deployment_name,
                messages=messages,
                temperature=0,
                top_p=0.1,
            )
            duration = time.time() - start_call
            current_app.logger.info(f"Response received from OpenAI in {duration:.2f} seconds.")
            return response.choices[0].message.content
        except Exception as e:
            duration = time.time() - start_call
            error_message = f"Azure OpenAI API request failed after {duration:.2f} seconds: {str(e)}"
            current_app.logger.error(error_message)
            raise Exception(error_message)
    
    def write_results_to_excel(self, results: List[str], output_column: str, header: str):
        """
        Write the results to a new column in the Excel file.
        
        Args:
            results (List[str]): List of results to write
            output_column (str): Column letter or name to write to
            header (str): Header value for the output column
        """
        # Load the workbook and get the active sheet
        workbook = load_workbook(self.excel_path)
        sheet = workbook.active
        
        # Determine the column index
        if len(output_column) == 1 and output_column.isalpha():
            col_idx = ord(output_column.upper()) - ord('A') + 1
        else:
            # If it's not a single letter, we'll need to find its index
            df = pd.read_excel(self.excel_path)
            if output_column in df.columns:
                col_idx = df.columns.get_loc(output_column) + 1
            else:
                # If the column doesn't exist, create it
                col_idx = len(df.columns) + 1
        
        # Write the header first
        header_text = f"{header}_translated" if header else "Translated"
        sheet.cell(row=1, column=col_idx, value=header_text)
        
        # Write the results to the Excel file, starting from row 2 (after header)
        for i, result in enumerate(results):
            try:
                # Parse the result as JSON to extract the values
                result_json = json.loads(result)
                
                # Write each value to the corresponding row (add 1 for header row)
                for row_idx, value in result_json.items():
                    sheet.cell(row=int(row_idx)+1, column=col_idx, value=value)
            except json.JSONDecodeError:
                # If the result is not valid JSON, write it as is
                sheet.cell(row=i+2, column=col_idx, value=result)  # +2 to account for header and 0-indexing
        
        # Save the workbook
        workbook.save(self.excel_path)

    def get_default_prompt(self, target_language: str) -> str:
        """
        Get the default translation prompt for the specified target language.

        Args:
            target_language (str): Target language for translation

        Returns:
            str: Default translation prompt
        """
        return f"""
        You are a professional translator. You will be provided with words or sentences that require translation.
        Translate each element into clear, accurate {target_language}, preserving technical terminology and meaning.
        Double check the meaning of words and phrases to ensure accuracy and correct context.

        IMPORTANT: Additional context for the translation may be provided in the user message. Use it to improve translation accuracy if present.

        You must format your response as a valid JSON object where the keys are the row numbers and the values are the translated {target_language} text. For example:
        {{"1": "30kg Industrial Washing Machine", "2": "Stainless Steel Dryer with Steam Function", "3": "Commercial Ironing System"}}

        Do not include any explanations or notes outside of this JSON structure.
        """

    def translate_column(self, column_name: str, target_language: str, max_rows: int = 200, file_context: str = None) -> Dict:
        """
        Translate a specific column in the Excel file.

        Args:
            column_name (str): Column name or letter to translate
            target_language (str): Target language for translation
            max_rows (int): Maximum rows per batch for API calls

        Returns:
            Dict: Translation result with success status and details
        """
        start_script = time.time()

        try:
            # Read the Excel column to JSON
            json_arrays, original_header = self.read_excel_column_to_json(column_name, max_rows)


            prompt = self.get_default_prompt(target_language)

            # Submit each JSON array to GPT and collect the responses
            responses = []
            for idx, json_obj in enumerate(json_arrays):
                current_app.logger.info(f"Processing batch {idx+1}/{len(json_arrays)}")
                response = self.submit_to_gpt(json_obj, prompt, file_context=file_context)
                responses.append(response)

            # Determine output column (next available column)
            df = pd.read_excel(self.excel_path)
            output_column = chr(ord('A') + len(df.columns))  # Next column letter

            # Write the results to the Excel file
            self.write_results_to_excel(responses, output_column, original_header)

            total_duration = time.time() - start_script
            current_app.logger.info(f"Processed {len(json_arrays)} JSON arrays and wrote results to column {output_column}")
            current_app.logger.info(f"Script completed in {total_duration:.2f} seconds.")

            return {
                'success': True,
                'message': f'Translation completed successfully',
                'batches_processed': len(json_arrays),
                'output_column': output_column,
                'original_column': column_name,
                'target_language': target_language,
                'duration': total_duration
            }

        except Exception as e:
            current_app.logger.error(f"Translation error: {e}")
            return {
                'success': False,
                'error': str(e)
            }

    def translate_multiple_columns(self, column_names: List[str], target_language: str, max_rows: int = 200, file_context: str = None) -> Dict:
        """
        Translate multiple columns in the Excel file.

        Args:
            column_names (List[str]): List of column names or letters to translate
            target_language (str): Target language for translation
            max_rows (int): Maximum rows per batch for API calls

        Returns:
            Dict: Translation result with success status and details
        """
        start_script = time.time()
        results = []

        try:
            for column_name in column_names:
                current_app.logger.info(f"Translating column: {column_name}")
                result = self.translate_column(column_name, target_language, max_rows, file_context=file_context)
                results.append({
                    'column': column_name,
                    'result': result
                })

                if not result['success']:
                    current_app.logger.error(f"Failed to translate column {column_name}: {result.get('error')}")

            total_duration = time.time() - start_script
            successful_translations = [r for r in results if r['result']['success']]

            return {
                'success': len(successful_translations) > 0,
                'message': f'Translated {len(successful_translations)}/{len(column_names)} columns successfully',
                'results': results,
                'target_language': target_language,
                'total_duration': total_duration
            }

        except Exception as e:
            current_app.logger.error(f"Multiple column translation error: {e}")
            return {
                'success': False,
                'error': str(e),
                'results': results
            }

    def get_excel_columns(self) -> List[str]:
        """
        Get the list of columns in the Excel file.

        Returns:
            List[str]: List of column names
        """
        try:
            df = pd.read_excel(self.excel_path)
            return df.columns.tolist()
        except Exception as e:
            current_app.logger.error(f"Error reading Excel columns: {e}")
            return []

