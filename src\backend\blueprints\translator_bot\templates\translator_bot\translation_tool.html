<!-- templates/translator_bot/translation_tool.html -->

{% extends "base.html" %}

{% block head_css %}
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
<link rel="stylesheet" href="{{ url_for('static', filename='translator_tool.css') }}">
<link href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css" rel="stylesheet" />
{% endblock %}

{% block content %}
<div class="translation-container">
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-lg-10 col-xl-8">
                <div class="translation-card">
                    <div class="card-header-custom position-relative text-center" style="display: flex; flex-direction: column; align-items: center; padding-top: 0;">
                        <span class="header-icon-top" style="font-size: 5rem; margin-bottom: 0.1rem; margin-top: 0; display: block;">
                            <i class="fas fa-language"></i>
                        </span>
                        <h1 class="mb-0" style="font-size: 2.5rem; font-weight: 700;">Document Translation Tool</h1>
                        <p class="mb-0 mt-2 opacity-75">
                            Translate Excel documents with AI
                        </p>
                    </div>
                    
                    <div class="card-body p-4">
                        <!-- File Upload Section -->
                        <div class="mb-4 mt-4">                            
                            <div class="upload-area" id="uploadArea">
                                <div class="upload-icon">
                                    <i class="fas fa-cloud-upload-alt"></i>
                                </div>
                                <h6>Drag & drop your file here</h6>
                                <p class="text-muted mb-3">or click to browse</p>
                                <p class="small text-muted">
                                    Supported formats: Excel (.xlsx), PowerPoint (.pptx), Word (.docx)
                                    <br>Maximum file size: 50MB
                                </p>
                                <input type="file" id="fileInput" accept=".xlsx,.pptx,.docx" style="display: none;">
                            </div>
                            
                            <div id="fileInfo" class="file-info" style="display: none;">
                                <div class="d-flex align-items-center">
                                    <i class="fas fa-file me-3 text-dark"></i>
                                    <div class="flex-grow-1">
                                        <div class="fw-bold" id="fileName"></div>
                                        <div class="small text-muted" id="fileDetails"></div>
                                    </div>
                                    <button class="remove-file-btn" id="removeFile" type="button">
                                        <i class="fas fa-times"></i>
                                    </button>
                                </div>
                            </div>

                            <!-- Loading Spinner -->
                            <div id="loadingSpinner" class="loading-spinner">
                                <div class="spinner"></div>
                                <div class="loading-text" id="loadingText">Uploading and processing file...</div>
                            </div>
                        </div>

                        <!-- Excel Column Selection -->
                        <div id="excelOptions" class="excel-options" style="display: none;">
                            <div class="excel-options-header">
                                <h5>
                                    <i class="fas fa-table me-2"></i>
                                    Column Selection
                                </h5>
                                <button type="button" class="select-all-btn" id="toggleAllColumns">
                                    <i class="fas fa-check-square me-2"></i>
                                    <span>Select All</span>
                                </button>
                            </div>
                            <p class="text-muted mb-3">Select which columns you want to translate:</p>

                            <div class="row" id="columnCheckboxes">
                                <!-- Checkboxes will be populated dynamically -->
                            </div>
                        </div>

                        <!-- File Context Input & Language Selection -->
                        <div id="languageSettings" class="excel-options mt-4" style="display: none;">
                            <div class="excel-options-header">
                                <h5>
                                    <i class="fas fa-globe me-2"></i>
                                    Language Settings
                                </h5>
                            </div>
                            <!-- Context Input -->
                            <div class="mb-3">
                                <label for="fileContext" class="form-label fw-semibold">File Context (optional)</label>
                                <textarea class="form-control" id="fileContext" rows="3" placeholder="Add any relevant context or instructions for the model to improve the translation accuracy."></textarea>
                            </div>
                            <div class="language-grid">
                                <div>
                                    <label for="sourceLanguage" class="form-label fw-semibold">Source Language</label>
                                    <select class="form-select form-select-custom" id="sourceLanguage">
                                        <option value="auto">Auto-detect</option>
                                        <option value="en">English</option>
                                        <option value="es">Spanish</option>
                                        <option value="fr">French</option>
                                        <option value="de">German</option>
                                        <option value="it">Italian</option>
                                        <option value="pt">Portuguese</option>
                                        <option value="ru">Russian</option>
                                        <option value="ja">Japanese</option>
                                        <option value="ko">Korean</option>
                                        <option value="zh">Chinese</option>
                                    </select>
                                </div>
                                <div>
                                    <label for="targetLanguage" class="form-label fw-semibold">Target Language(s)</label>
                                    <select class="form-select form-select-custom" id="targetLanguage" multiple="multiple" style="width: 100%;">
                                        <option value="en">English</option>
                                        <option value="es">Spanish</option>
                                        <option value="fr">French</option>
                                        <option value="de">German</option>
                                        <option value="it">Italian</option>
                                        <option value="pt">Portuguese</option>
                                        <option value="ru">Russian</option>
                                        <option value="ja">Japanese</option>
                                        <option value="ko">Korean</option>
                                        <option value="zh">Chinese</option>
                                        <option value="ar">Arabic</option>
                                        <option value="hi">Hindi</option>
                                        <option value="th">Thai</option>
                                        <option value="vi">Vietnamese</option>
                                        <option value="nl">Dutch</option>
                                        <option value="sv">Swedish</option>
                                        <option value="no">Norwegian</option>
                                        <option value="da">Danish</option>
                                        <option value="fi">Finnish</option>
                                        <option value="pl">Polish</option>
                                    </select>
                                </div>
                            </div>
                        </div>

                        <!-- Translation Button -->

                        <div class="text-center mt-4">
                            <button class="translate-link-custom btn-lg" id="translateBtn" disabled>
                                <i class="fas fa-magic me-2"></i>
                                Start Translation
                            </button>
                        </div>


                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block tail_js %}
<!-- Select2 JS -->
<script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js"></script>
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Initialize Select2 for targetLanguage
    $('#targetLanguage').select2({
        placeholder: 'Select target language(s)',
        allowClear: true,
        width: 'resolve'
    });


    const uploadArea = document.getElementById('uploadArea');
    const fileInput = document.getElementById('fileInput');
    const fileInfo = document.getElementById('fileInfo');
    const fileName = document.getElementById('fileName');
    const fileDetails = document.getElementById('fileDetails');
    const removeFile = document.getElementById('removeFile');
    const excelOptions = document.getElementById('excelOptions');
    const columnCheckboxes = document.getElementById('columnCheckboxes');
    const toggleAllColumns = document.getElementById('toggleAllColumns');
    const translateBtn = document.getElementById('translateBtn');
    const targetLanguage = document.getElementById('targetLanguage');

    const loadingSpinner = document.getElementById('loadingSpinner');
    const loadingText = document.getElementById('loadingText');
    const languageSettings = document.getElementById('languageSettings');
    
    let selectedFile = null;

    // Ensure the translate button is always disabled on page load
    translateBtn.hidden = true;
    // Helper function to show alerts
    function showAlert(message, type = 'info') {
        // Create a simple alert div
        const alertDiv = document.createElement('div');
        alertDiv.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
        alertDiv.style.cssText = 'top: 20px; right: 20px; z-index: 9999; max-width: 400px;';
        alertDiv.innerHTML = `
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;
        document.body.appendChild(alertDiv);

        // Auto-remove after 5 seconds
        setTimeout(() => {
            if (alertDiv.parentNode) {
                alertDiv.parentNode.removeChild(alertDiv);
            }
        }, 5000);
    }

    // Upload area click handler
    uploadArea.addEventListener('click', () => {
        fileInput.click();
    });

    // Drag and drop handlers
    uploadArea.addEventListener('dragover', (e) => {
        e.preventDefault();
        uploadArea.classList.add('dragover');
    });

    uploadArea.addEventListener('dragleave', () => {
        uploadArea.classList.remove('dragover');
    });

    uploadArea.addEventListener('drop', (e) => {
        e.preventDefault();
        uploadArea.classList.remove('dragover');
        const files = e.dataTransfer.files;
        if (files.length > 0) {
            handleFileSelect(files[0]);
        }
    });

    // File input change handler
    fileInput.addEventListener('change', (e) => {
        if (e.target.files.length > 0) {
            handleFileSelect(e.target.files[0]);
        }
    });

    // Remove file handler
    removeFile.addEventListener('click', () => {
        selectedFile = null;
        fileInfo.style.display = 'none';
        uploadArea.style.display = 'block';
        excelOptions.style.display = 'none';
        loadingSpinner.style.display = 'none';
        fileInput.value = '';
        languageSettings.style.display = 'none';
        updateTranslateButton();
    });

    // Target language change handler (Select2 event)
    $('#targetLanguage').on('change', updateTranslateButton);

    // Update button state when columns are selected/deselected
    columnCheckboxes.addEventListener('change', updateTranslateButton);

    // Translate button handler
    translateBtn.addEventListener('click', startTranslation);

    // Toggle all columns button handler
    toggleAllColumns.addEventListener('click', function() {
        const checkboxes = columnCheckboxes.querySelectorAll('input[type="checkbox"]');
        const allChecked = Array.from(checkboxes).every(cb => cb.checked);

        checkboxes.forEach(checkbox => {
            checkbox.checked = !allChecked;
        });

        // Update button text and icon
        const icon = this.querySelector('i');
        const text = this.querySelector('span') || this.childNodes[this.childNodes.length - 1];

        if (allChecked) {
            icon.className = 'fas fa-square me-1';
            this.innerHTML = '<i class="fas fa-square me-1"></i>Select All';
        } else {
            icon.className = 'fas fa-check-square me-1';
            this.innerHTML = '<i class="fas fa-check-square me-1"></i>Deselect All';
        }

        updateTranslateButton();
    });

    function handleFileSelect(file) {
        // Validate file type
        const allowedTypes = ['.xlsx', '.pptx', '.docx'];
        const fileExtension = '.' + file.name.split('.').pop().toLowerCase();
        
        if (!allowedTypes.includes(fileExtension)) {
            showAlert('Please select a valid file type: Excel (.xlsx), PowerPoint (.pptx), or Word (.docx)', 'warning');
            return;
        }

        // Validate file size (50MB limit)
        if (file.size > 50 * 1024 * 1024) {
            showAlert('File size must be less than 50MB', 'warning');
            return;
        }

        selectedFile = file;
        // Hide language settings until upload/processing is done
        languageSettings.style.display = 'none';
        // Show loading spinner
        uploadArea.style.display = 'none';
        loadingSpinner.style.display = 'block';
        loadingText.textContent = 'Uploading and processing file...';

        // Upload file and get column information for Excel files
        if (fileExtension === '.xlsx') {
            uploadFileToServer(file);
        } else {
            // For non-Excel files, simulate a brief loading time
            setTimeout(() => {
                loadingSpinner.style.display = 'none';
                
                // Update file info display
                fileName.textContent = file.name;
                fileDetails.textContent = `${(file.size / 1024 / 1024).toFixed(2)} MB • ${fileExtension.toUpperCase().substring(1)} file`;
                fileInfo.style.display = 'block';
                
                excelOptions.style.display = 'none';
                updateTranslateButton();
            }, 800);
        }
    }

    function uploadFileToServer(file) {
        const formData = new FormData();
        formData.append('file', file);
        
        loadingText.textContent = 'Uploading file...';

        fetch('/translator/api/upload', {
            method: 'POST',
            body: formData
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                loadingText.textContent = 'Getting Excel columns...';
                // For Excel files, get columns from the new endpoint
                if (file.name.toLowerCase().endsWith('.xlsx')) {
                    getExcelColumns();
                } else {
                    finishFileLoading();
                }
            } else {
                loadingSpinner.style.display = 'none';
                uploadArea.style.display = 'block';
                alert('Error uploading file: ' + data.error);
            }
        })
        .catch(error => {
            console.error('Upload error:', error);
            loadingSpinner.style.display = 'none';
            uploadArea.style.display = 'block';
            alert('Error uploading file');
        });
    }

    function finishFileLoading() {
        // Hide loading spinner and show file info
        loadingSpinner.style.display = 'none';
        // Show language settings now that upload/processing is done
        languageSettings.style.display = 'block';
        // Update file info display
        fileName.textContent = selectedFile.name;
        const fileExtension = '.' + selectedFile.name.split('.').pop().toLowerCase();
        fileInfo.style.display = 'block';
        updateTranslateButton();
    }

    function getExcelColumns() {
        loadingText.textContent = 'Analyzing Excel columns...';
        
        fetch('/translator/api/columns', {
            method: 'GET'
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showExcelOptions(data.columns, null);
                finishFileLoading();
            } else {
                console.error('Error getting columns:', data.error);
                loadingSpinner.style.display = 'none';
                uploadArea.style.display = 'block';
                alert('Error analyzing Excel file: ' + data.error);
            }
        })
        .catch(error => {
            console.error('Get columns error:', error);
            loadingSpinner.style.display = 'none';
            uploadArea.style.display = 'block';
            alert('Error analyzing Excel file');
        });
    }

    function showExcelOptions(columns, preview) {
        columnCheckboxes.innerHTML = '';
        // Hide language settings while loading columns
        languageSettings.style.display = 'none';

        // Show column checkboxes
        const checkboxContainer = document.createElement('div');
        checkboxContainer.className = 'row';

        columns.forEach((column, index) => {
            const colDiv = document.createElement('div');
            colDiv.className = 'col-md-6 column-checkbox';

            colDiv.innerHTML = `
                <div class="form-check">
                    <input class="form-check-input" type="checkbox" value="${column}" id="col${index}" checked>
                    <label class="form-check-label" for="col${index}">
                        ${column}
                    </label>
                </div>
            `;

            checkboxContainer.appendChild(colDiv);
        });

        columnCheckboxes.appendChild(checkboxContainer);

        // Initialize toggle button state (all checkboxes are checked by default)
        toggleAllColumns.innerHTML = '<i class="fas fa-check-square me-1"></i>Deselect All';

        excelOptions.style.display = 'block';

        // Show language settings after columns are loaded
        languageSettings.style.display = 'block';

        translateBtn.hidden = false;
    }
    
    function showTranslationPreview() {
        if (!selectedFile || !targetLanguage.value) {
            alert('Please select a file and target language');
            return;
        }

        // Get selected columns
        const checkboxes = columnCheckboxes.querySelectorAll('input[type="checkbox"]:checked');
        const selectedColumns = Array.from(checkboxes).map(cb => cb.value);

        if (selectedColumns.length === 0) {
            alert('Please select at least one column to translate');
            return;
        }

        // For now, preview only the first selected column
        const columnToPreview = selectedColumns[0];

        // Call preview API with new format
        const fileContext = document.getElementById('fileContext').value;
        fetch('/translator/api/preview', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                column_name: columnToPreview,
                target_language: getLanguageName(targetLanguage.value),
                file_context: fileContext
            })
        })
        .then(response => response.json())
        .then(data => {
            if (!data.success) {
                alert('Preview failed: ' + data.error);
            }
        })
        .catch(error => {
            console.error('Preview error:', error);
            alert('Preview failed');
        });
    }
    
    function getLanguageName(code) {
        const languages = {
            'en': 'English', 'es': 'Spanish', 'fr': 'French', 'de': 'German',
            'it': 'Italian', 'pt': 'Portuguese', 'ru': 'Russian', 'ja': 'Japanese',
            'ko': 'Korean', 'zh': 'Chinese', 'ar': 'Arabic', 'hi': 'Hindi',
            'th': 'Thai', 'vi': 'Vietnamese', 'nl': 'Dutch', 'sv': 'Swedish',
            'no': 'Norwegian', 'da': 'Danish', 'fi': 'Finnish', 'pl': 'Polish'
        };
        return languages[code] || code;
    }

    function getSelectedTargetLanguages() {
        // Returns an array of selected language codes from Select2
        return $('#targetLanguage').val() || [];
    }

    function updateTranslateButton() {
        const hasFile = selectedFile !== null;
        const selectedLangs = getSelectedTargetLanguages();
        translateBtn.disabled = !(hasFile && selectedLangs.length > 0);
    }



    function startTranslation() {
        const selectedLangs = getSelectedTargetLanguages();
        if (!selectedFile || selectedLangs.length === 0) {
            alert('Please select a file and at least one target language');
            return;
        }
        // Get selected columns for Excel files
        let selectedColumns = [];
        if (selectedFile.name.toLowerCase().endsWith('.xlsx')) {
            const checkboxes = columnCheckboxes.querySelectorAll('input[type="checkbox"]:checked');
            selectedColumns = Array.from(checkboxes).map(cb => cb.value);
        }

        translateBtn.disabled = true;
        translateBtn.textContent = 'Translating...';

        // Start translation
        const fileContext = document.getElementById('fileContext').value;
        const translationData = {
            target_languages: selectedLangs,
            source_language: document.getElementById('sourceLanguage').value,
            selected_columns: selectedColumns,
            file_type: '.' + selectedFile.name.split('.').pop().toLowerCase(),
            original_filename: selectedFile.name,
            file_context: fileContext
        };
        fetch('/translator/api/translate', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(translationData)
        })
        .then(response => response.json())
        .then(data => {
            translateBtn.disabled = false;
            translateBtn.textContent = 'Translate';

            if (data.success) {
                // Show success message
                showAlert('Translation completed successfully!', 'success');

                // Remove any existing download links
                const existingLinks = document.querySelectorAll('.download-link-custom');
                existingLinks.forEach(link => link.remove());

                // Create download link
                const downloadLink = document.createElement('a');
                downloadLink.className = 'btn btn-success btn-lg mt-3 download-link-custom';
                downloadLink.style.display = 'block';
                downloadLink.style.margin = '20px auto';
                downloadLink.style.width = 'fit-content';

                if (data.zip_file) {
                    // Multiple languages - show zip download
                    const zipFileParam = encodeURIComponent(data.zip_file);
                    downloadLink.href = `/translator/api/download/current?zip_file=${zipFileParam}`;
                    downloadLink.innerHTML = '<i class="fas fa-download me-2"></i>Download All Translations (ZIP)';
                } else {
                    // Single language - show single file download
                    downloadLink.href = '/translator/api/download/current';
                    downloadLink.innerHTML = '<i class="fas fa-download me-2"></i>Download Translated File';
                }

                // Insert after translate button
                translateBtn.parentNode.insertBefore(downloadLink, translateBtn.nextSibling);
            } else {
                showAlert('Translation failed: ' + (data.error || 'Unknown error'), 'danger');
            }
        })
        .catch(error => {
            console.error('Translation error:', error);
            translateBtn.disabled = false;
            translateBtn.textContent = 'Translate';
            showAlert('Translation failed due to network error', 'danger');
        });
    }
});
</script>
{% endblock %}
